"use client";

import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>l, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Compass, Lightbulb, Settings as Cog } from "lucide-react";
import { Too<PERSON>ip, TooltipTrigger, TooltipContent } from "@/components/ui/tooltip";
import { useState } from "react";

interface PresetsPanelProps {
  selectedPreset: string | null;
  onSelectPreset: (presetId: string) => void;
  pulseIntensity: 'light' | 'medium' | 'heavy';
}

interface Preset {
  id: string;
  name: string;
  description: string;
  icon: typeof Sparkles;
  status: "available" | "coming-soon";
  category: "basic" | "artistic" | "effects";
}

const presets: Preset[] = [
  {
    id: "pulse-light",
    name: "Light Pulse",
    description: "Subtle audio-reactive pulsing effects",
    icon: Zap,
    status: "available",
    category: "basic"
  },
  {
    id: "pulse-medium",
    name: "Medium Pulse",
    description: "Balanced audio-reactive pulsing effects",
    icon: Zap,
    status: "available",
    category: "basic"
  },
  {
    id: "pulse-heavy",
    name: "Heavy Pulse",
    description: "Dynamic audio-reactive pulsing effects",
    icon: Zap,
    status: "available",
    category: "basic"
  },
  {
    id: "particles",
    name: "Particles",
    description: "Floating particles that react to the music",
    icon: Snowflake,
    status: "coming-soon",
    category: "effects"
  },
  {
    id: "paint",
    name: "Paint",
    description: "Fluid paint-like visual effects",
    icon: Palette,
    status: "coming-soon",
    category: "artistic"
  },
  {
    id: "sketch",
    name: "Sketch",
    description: "Hand-drawn animation style",
    icon: Pencil,
    status: "coming-soon",
    category: "artistic"
  },
  {
    id: "blur",
    name: "Blur",
    description: "Smooth blur transitions with the beat",
    icon: Scan,
    status: "coming-soon",
    category: "effects"
  },
  {
    id: "glitch",
    name: "Glitch",
    description: "Digital glitch and distortion effects",
    icon: Sparkles,
    status: "coming-soon",
    category: "effects"
  },
  {
    id: "kaleidoscope",
    name: "Kaleidoscope",
    description: "Mesmerizing kaleidoscopic patterns",
    icon: Compass,
    status: "coming-soon",
    category: "effects"
  },
  {
    id: "lighting",
    name: "Lighting",
    description: "Dynamic lighting and glow effects",
    icon: Lightbulb,
    status: "coming-soon",
    category: "basic"
  }
];

export default function PresetsPanel({ selectedPreset, onSelectPreset, pulseIntensity }: PresetsPanelProps) {
  const [openSettings, setOpenSettings] = useState<string | null>(null);
  const orderedPresetIds = [
    "pulse-heavy", // Pulse
    "glitch",
    "lighting",
    "paint",
    "sketch",
    "particles",
    "blur",
    "kaleidoscope"
  ];
  const orderedPresets = orderedPresetIds.map(id => presets.find(p => p.id === id)).filter(Boolean) as Preset[];

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold mb-2">Visual Presets</h2>
        <p className="text-muted-foreground">
          Choose a base visual style for your video.
        </p>
      </div>
      <div className="flex flex-col gap-3">
        {orderedPresets.map((preset) => {
          const isSelected = selectedPreset === preset.id || (preset.id === "pulse-heavy" && selectedPreset === "pulse");
          const isPulse = preset.id === "pulse-heavy";
          const isAvailable = preset.status === "available";
          const showSettings = openSettings === preset.id;
          return (
            <div key={preset.id} className="w-full">
              <Card
                className={cn(
                  "relative group cursor-pointer transition-all duration-200 flex-1 w-full",
                  isSelected ? [
                    "bg-accent",
                    "ring-2 ring-primary",
                    "shadow-lg",
                  ] : [
                    "hover:shadow-md",
                    "hover:bg-accent/5",
                  ],
                  !isAvailable && "opacity-50 cursor-not-allowed"
                )}
                onClick={() => {
                  if (!isAvailable) return;
                  if (preset.id === "pulse-heavy") {
                    onSelectPreset("pulse-heavy");
                  } else {
                    onSelectPreset(preset.id);
                  }
                }}
              >
                <div className="p-4 flex items-start gap-3">
                  <div className={cn(
                    "p-2 rounded-md transition-colors duration-200",
                    isSelected ? [
                      "bg-primary text-primary-foreground",
                      "shadow-sm",
                    ] : [
                      "bg-muted",
                      "group-hover:bg-accent/10",
                    ]
                  )}>
                    <preset.icon className="h-5 w-5" />
                  </div>
                  <div className="space-y-1 flex-1">
                    <div className="flex items-center gap-2">
                      <h4 className={cn(
                        "font-medium transition-colors duration-200",
                        isSelected ? "text-white" : "text-foreground",
                      )}>
                        {preset.id === "pulse-heavy" ? "Pulse" : preset.name}
                      </h4>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <button type="button" tabIndex={-1} className={cn(
                            "ml-1 focus:outline-none",
                            isSelected ? "text-white hover:text-white" : "text-muted-foreground hover:text-primary"
                          )} onClick={e => e.stopPropagation()}>
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 24 24"><circle cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="2" fill="none"/><text x="12" y="16" textAnchor="middle" fontSize="12" fill="currentColor" fontFamily="Arial, sans-serif">i</text></svg>
                          </button>
                        </TooltipTrigger>
                        <TooltipContent side="top">
                          {preset.description}
                        </TooltipContent>
                      </Tooltip>
                      <button
                        type="button"
                        tabIndex={-1}
                        className={cn(
                          "ml-1 focus:outline-none transition-colors",
                          isSelected ? "text-white hover:text-white" : "text-muted-foreground hover:text-primary"
                        )}
                        onClick={e => {
                          e.stopPropagation();
                          setOpenSettings(openSettings === preset.id ? null : preset.id);
                        }}
                        aria-label="Settings"
                      >
                        <Cog className="w-5 h-5" />
                      </button>
                    </div>
                  </div>
                </div>
              </Card>
              {/* Settings dropdown below the card */}
              <div
                className={cn(
                  "overflow-hidden transition-all duration-300",
                  showSettings ? "max-h-40 opacity-100 py-2" : "max-h-0 opacity-0 py-0"
                )}
                style={{}}
              >
                {isPulse ? (
                  <div className="flex items-center gap-2 bg-muted rounded-lg px-3 py-2 w-fit ml-12">
                    <span className="text-sm text-muted-foreground">Intensity:</span>
                    <div className="flex rounded-lg overflow-hidden">
                      {["light", "medium", "heavy"].map((level) => (
                        <button
                          key={level}
                          onClick={() => onSelectPreset(`pulse-${level}`)}
                          className={cn(
                            "px-3 py-1 text-sm font-medium transition-colors",
                            pulseIntensity === level
                              ? "bg-accent text-accent-foreground"
                              : "hover:bg-accent/50 text-muted-foreground"
                          )}
                          type="button"
                        >
                          {level.charAt(0).toUpperCase() + level.slice(1)}
                        </button>
                      ))}
                    </div>
                  </div>
                ) : (
                  <div className="text-sm text-muted-foreground bg-muted rounded-lg px-3 py-2 w-fit ml-12">No settings available.</div>
                )}
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
}
