'use client';

import { createFFmpeg, fetchFile } from '@ffmpeg/ffmpeg';

// Constants for pulse effect
const ANALYSIS_WINDOW_DURATION_SEC = 0.05; // 50ms window for audio analysis
const PULSE_MAX_SCALE_INCREASE = 0.3;    // e.g., visual scale from 1.0 to 1.3
const RMS_NORMALIZATION_BOOST = 4.0;     // Empirical value to boost RMS for pulse effect

interface ExportParams {
  songDataUri: string;
  visualMediaSrc: string;
  visualMediaType: "image" | "video";
  outputWidth: number;
  outputHeight: number;
  fps: number;
  onProgress: (progress: number, info?: string) => void;
}

interface ExportResult {
  blob: Blob;
  actualMimeType: string;
}

// Helper to calculate RMS of an audio segment
function calculateRMS(audioSegment: Float32Array): number {
  if (audioSegment.length === 0) return 0;
  let sumOfSquares = 0;
  for (const sample of audioSegment) {
    sumOfSquares += sample * sample;
  }
  return Math.sqrt(sumOfSquares / audioSegment.length);
}

// Get audio energy at a specific time point
function getAudioEnergyAtTime(audioBuffer: AudioBuffer, timeInSeconds: number, windowDuration: number): number {
  const sampleRate = audioBuffer.sampleRate;
  const startSample = Math.floor(timeInSeconds * sampleRate);
  const windowSamples = Math.floor(windowDuration * sampleRate);
  
  // Create a buffer for each channel
  const channelData: Float32Array[] = [];
  for (let channel = 0; channel < audioBuffer.numberOfChannels; channel++) {
    channelData.push(new Float32Array(windowSamples));
    audioBuffer.copyFromChannel(channelData[channel], channel, startSample);
  }
  
  // Mix down to mono if needed
  let monoData: Float32Array;
  if (audioBuffer.numberOfChannels === 1) {
    monoData = channelData[0];
  } else {
    monoData = new Float32Array(windowSamples);
    for (let i = 0; i < windowSamples; i++) {
      let sum = 0;
      for (let channel = 0; channel < audioBuffer.numberOfChannels; channel++) {
        sum += channelData[channel][i];
      }
      monoData[i] = sum / audioBuffer.numberOfChannels;
    }
  }
  
  const rms = calculateRMS(monoData);
  return Math.min(rms * RMS_NORMALIZATION_BOOST, 1.0);
}

export async function exportWithFFmpeg({
  songDataUri,
  visualMediaSrc,
  visualMediaType,
  outputWidth,
  outputHeight,
  fps,
  onProgress,
}: ExportParams): Promise<ExportResult> {
  console.log(`[FFmpegExporter] Starting export. SongDataUri length: ${songDataUri?.length}, VisualMediaSrc length: ${visualMediaSrc?.length}`);
  onProgress(0.0, "Initializing FFmpeg...");

  // Create and load FFmpeg instance
  const ffmpeg = createFFmpeg({
    log: true,
    progress: ({ ratio }) => {
      if (ratio && !isNaN(ratio)) {
        // FFmpeg encoding progress (0.5-1.0 range)
        onProgress(0.5 + ratio * 0.5, `Encoding video: ${Math.round(ratio * 100)}%`);
      }
    },
    corePath: 'http://localhost:9002/ffmpeg/ffmpeg-core.js',
    wasmPath: 'http://localhost:9002/ffmpeg/ffmpeg-core.wasm',
    workerPath: 'http://localhost:9002/ffmpeg/ffmpeg-core.worker.js',
  });
  
  if (!ffmpeg.isLoaded()) {
    await ffmpeg.load();
  }
  
  onProgress(0.05, "FFmpeg loaded. Processing audio...");

  try {
    // 1. Load and decode audio
    const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
    const audioResponse = await fetch(songDataUri);
    const audioArrayBuffer = await audioResponse.arrayBuffer();
    const audioBuffer = await audioContext.decodeAudioData(audioArrayBuffer.slice(0));
    
    const audioDuration = audioBuffer.duration;
    console.log(`[FFmpegExporter] Audio decoded. Duration: ${audioDuration.toFixed(3)}s`);
    onProgress(0.1, `Audio decoded (${audioDuration.toFixed(1)}s)`);

    // 2. Load visual media
    onProgress(0.15, "Loading visual media...");
    let visualMedia: HTMLImageElement | HTMLVideoElement;
    
    if (visualMediaType === "image") {
      const image = new Image();
      image.crossOrigin = "anonymous";
      await new Promise<void>((resolve, reject) => {
        image.onload = () => resolve();
        image.onerror = (err) => reject(new Error(`Failed to load image: ${err}`));
        image.src = visualMediaSrc;
      });
      visualMedia = image;
    } else {
      const video = document.createElement('video');
      video.crossOrigin = "anonymous";
      video.muted = true;
      await new Promise<void>((resolve, reject) => {
        video.onloadeddata = () => resolve();
        video.onerror = (err) => reject(new Error(`Failed to load video: ${err}`));
        video.src = visualMediaSrc;
        video.load();
      });
      visualMedia = video;
      await visualMedia.play();
    }
    
    onProgress(0.2, "Generating frames...");
    
    // 3. Create canvas for rendering frames
    const canvas = document.createElement('canvas');
    canvas.width = outputWidth;
    canvas.height = outputHeight;
    const ctx = canvas.getContext('2d')!;
    
    // 4. Generate frames
    const totalFrames = Math.ceil(audioDuration * fps);
    const frameFiles: string[] = [];
    
    for (let i = 0; i < totalFrames; i++) {
      const frameTime = i / fps;
      const frameProgress = i / totalFrames;
      
      if (i % Math.max(1, Math.floor(totalFrames / 20)) === 0) {
        onProgress(0.2 + frameProgress * 0.3, `Generating frame ${i+1}/${totalFrames}`);
      }
      
      // Get audio energy for this frame
      const audioEnergy = getAudioEnergyAtTime(audioBuffer, frameTime, ANALYSIS_WINDOW_DURATION_SEC);
      const scale = 1.0 + audioEnergy * PULSE_MAX_SCALE_INCREASE;
      
      // Clear canvas
      ctx.fillStyle = 'black';
      ctx.fillRect(0, 0, outputWidth, outputHeight);
      
      // Draw visual media with pulse effect
      let naturalWidth: number, naturalHeight: number;
      
      if (visualMediaType === "image") {
        naturalWidth = (visualMedia as HTMLImageElement).naturalWidth;
        naturalHeight = (visualMedia as HTMLImageElement).naturalHeight;
      } else {
        const video = visualMedia as HTMLVideoElement;
        naturalWidth = video.videoWidth;
        naturalHeight = video.videoHeight;
        
        // Update video position if needed
        if (video.duration > 0) {
          const videoTime = frameTime % video.duration;
          if (Math.abs(video.currentTime - videoTime) > 0.1) {
            video.currentTime = videoTime;
            // Wait for seeking to complete
            await new Promise<void>(resolve => {
              const onSeeked = () => {
                video.removeEventListener('seeked', onSeeked);
                resolve();
              };
              video.addEventListener('seeked', onSeeked, { once: true });
            });
          }
        }
      }
      
      // Calculate dimensions to maintain aspect ratio
      const mediaAspectRatio = naturalWidth / naturalHeight;
      const canvasAspectRatio = outputWidth / outputHeight;
      let baseDrawWidth, baseDrawHeight;
      
      if (mediaAspectRatio > canvasAspectRatio) {
        baseDrawWidth = outputWidth;
        baseDrawHeight = outputWidth / mediaAspectRatio;
      } else {
        baseDrawHeight = outputHeight;
        baseDrawWidth = outputHeight * mediaAspectRatio;
      }
      
      const finalDrawWidth = baseDrawWidth * scale;
      const finalDrawHeight = baseDrawHeight * scale;
      const offsetX = (outputWidth - finalDrawWidth) / 2;
      const offsetY = (outputHeight - finalDrawHeight) / 2;
      
      ctx.drawImage(visualMedia, offsetX, offsetY, finalDrawWidth, finalDrawHeight);
      
      // Convert canvas to image and save to FFmpeg virtual filesystem
      const frameDataUrl = canvas.toDataURL('image/jpeg', 0.95);
      const frameData = atob(frameDataUrl.split(',')[1]);
      const frameUint8Array = new Uint8Array(frameData.length);
      for (let j = 0; j < frameData.length; j++) {
        frameUint8Array[j] = frameData.charCodeAt(j);
      }
      
      const frameFilename = `frame_${i.toString().padStart(6, '0')}.jpg`;
      ffmpeg.FS('writeFile', frameFilename, frameUint8Array);
      frameFiles.push(frameFilename);
    }
    
    onProgress(0.5, "Frames generated. Encoding audio...");
    
    // 5. Write audio file to FFmpeg virtual filesystem
    const audioBlob = await fetch(songDataUri).then(r => r.blob());
    const audioFile = new Uint8Array(await audioBlob.arrayBuffer());
    ffmpeg.FS('writeFile', 'audio.mp3', audioFile);
    
    // 6. Create FFmpeg command to combine frames and audio
    await ffmpeg.run(
      '-framerate', fps.toString(),
      '-i', 'frame_%06d.jpg',
      '-i', 'audio.mp3',
      '-c:v', 'libx264',
      '-preset', 'medium',
      '-crf', '23',
      '-pix_fmt', 'yuv420p',
      '-c:a', 'aac',
      '-b:a', '128k',
      '-shortest',
      'output.mp4'
    );
    
    // 7. Read the output file
    const outputData = ffmpeg.FS('readFile', 'output.mp4');
    
    // 8. Clean up files from virtual filesystem
    frameFiles.forEach(file => {
      try {
        ffmpeg.FS('unlink', file);
      } catch (e) {
        console.warn(`Could not unlink ${file}:`, e);
      }
    });
    
    try {
      ffmpeg.FS('unlink', 'audio.mp3');
      ffmpeg.FS('unlink', 'output.mp4');
    } catch (e) {
      console.warn('Error cleaning up files:', e);
    }
    
    // 9. Create blob from output data
    const outputBlob = new Blob([outputData], { type: 'video/mp4' }); // Use outputData directly

    onProgress(1.0, "Export complete!");
    return {
      blob: outputBlob,
      actualMimeType: 'video/mp4'
    };
  } catch (error) {
    console.error('[FFmpegExporter] Export failed:', error);
    throw error;
  } finally {
    // Clean up
    if (ffmpeg && ffmpeg.isLoaded()) {
      try {
        await ffmpeg.exit();
      } catch (e) {
        console.warn('Error exiting FFmpeg:', e);
      }
    }
  }
}