"use client";

import Image from "next/image";
import { useEffect, useRef, useState } from "react";
import type { BeatData } from "@/lib/types"; // BeatData might be less used now
import { cn } from "@/lib/utils";

const ANALYSIS_WINDOW_DURATION_SEC = 0.05; // 50ms window for audio analysis
const PULSE_MAX_SCALE_INCREASE = 0.3;    // e.g. scale from 1.0 to 1.3
const RMS_NORMALIZATION_BOOST = 4.0;     // Empirical value to boost RMS for pulse effect

// Constants for different pulse intensity levels
const PULSE_LEVELS = {
  light: {
    maxScale: 0.1,
    rmsBoost: 2.0
  },
  medium: {
    maxScale: 0.2,
    rmsBoost: 3.0
  },
  heavy: {
    maxScale: 0.3,
    rmsBoost: 4.0
  }
} as const;

interface PreviewPlayerProps {
  visualMediaSrc: string | null;
  visualMediaType: "image" | "video" | null;
  beatData: BeatData | null; // Potentially unused if direct audio analysis is preferred
  audioElementRef: React.RefObject<HTMLAudioElement>; // Main audio element for playback
  isPlaying: boolean;
  selectedPreset: string | null;
  songDataUriForPulse: string | null; // Separate URI for internal AudioContext analysis if needed
  pulseIntensity: keyof typeof PULSE_LEVELS;
}

export default function PreviewPlayer({
  visualMediaSrc,
  visualMediaType,
  // beatData, // beatData is less relevant if using direct audio analysis for pulse
  audioElementRef,
  isPlaying,
  selectedPreset,
  songDataUriForPulse, 
  pulseIntensity = 'heavy', // default to heavy for backward compatibility
}: PreviewPlayerProps) {
  const [currentScale, setCurrentScale] = useState(1.0);
  const animationFrameIdRef = useRef<number | null>(null);

  // For "pulse" effect: AudioContext and AudioBuffer for direct analysis
  const internalAudioContextRef = useRef<AudioContext | null>(null);
  const audioBufferRef = useRef<AudioBuffer | null>(null);
  const lastAnalyzedSongUriRef = useRef<string | null>(null);

  // Effect to setup AudioContext and load AudioBuffer for analysis if "pulse" is selected
  useEffect(() => {
    const setupAudioAnalysis = async () => {
      if (selectedPreset === "pulse" && songDataUriForPulse && songDataUriForPulse !== lastAnalyzedSongUriRef.current) {
        lastAnalyzedSongUriRef.current = songDataUriForPulse; // Mark as attempting to process
        try {
          if (internalAudioContextRef.current && internalAudioContextRef.current.state !== 'closed') {
            await internalAudioContextRef.current.close();
          }
          internalAudioContextRef.current = new (window.AudioContext || (window as any).webkitAudioContext)();
          
          const response = await fetch(songDataUriForPulse);
          const arrayBuffer = await response.arrayBuffer();
          audioBufferRef.current = await internalAudioContextRef.current.decodeAudioData(arrayBuffer);
          console.log("AudioBuffer loaded for pulse effect analysis.");
        } catch (e) {
          console.error("Error setting up AudioBuffer for pulse effect:", e);
          audioBufferRef.current = null;
          lastAnalyzedSongUriRef.current = null; // Reset if failed, to allow retry
           if (internalAudioContextRef.current && internalAudioContextRef.current.state !== 'closed') {
            internalAudioContextRef.current.close().catch(err => console.error("Error closing internal AudioContext after failure:", err));
            internalAudioContextRef.current = null;
          }
        }
      } else if (selectedPreset !== "pulse") {
        audioBufferRef.current = null; // Clear buffer if preset changes
        lastAnalyzedSongUriRef.current = null;
         if (internalAudioContextRef.current && internalAudioContextRef.current.state !== 'closed') {
            internalAudioContextRef.current.close().catch(err => console.error("Error closing internal AudioContext on preset change:", err));
            internalAudioContextRef.current = null;
        }
      }
    };

    setupAudioAnalysis();

    // Cleanup on component unmount or if songDataUriForPulse/selectedPreset changes significantly
    return () => {
      if (internalAudioContextRef.current && internalAudioContextRef.current.state !== 'closed') {
        internalAudioContextRef.current.close().catch(err => console.error("Error closing internal AudioContext on cleanup:", err));
      }
      internalAudioContextRef.current = null;
      audioBufferRef.current = null;
      lastAnalyzedSongUriRef.current = null;
    };
  }, [selectedPreset, songDataUriForPulse]);


  // Function to calculate audio energy from the AudioBuffer at a given time
  const calculateAudioEnergyAtTime = (currentTime: number): number => {
    if (!audioBufferRef.current || !internalAudioContextRef.current || internalAudioContextRef.current.state === 'closed') return 0;

    const audioBuffer = audioBufferRef.current;
    const sampleRate = audioBuffer.sampleRate;
    const windowSamples = Math.floor(ANALYSIS_WINDOW_DURATION_SEC * sampleRate);
    
    let startSample = Math.floor(currentTime * sampleRate);
    // Ensure startSample is not negative (e.g. if currentTime is slightly off)
    startSample = Math.max(0, startSample);

    let endSample = startSample + windowSamples;
    
    // Clamp endSample to buffer length
    endSample = Math.min(endSample, audioBuffer.length);
    // Ensure startSample is also less than buffer length, and less than endSample
    startSample = Math.min(startSample, endSample);


    if (startSample >= audioBuffer.length || startSample >= endSample) return 0;

    const channelData = audioBuffer.getChannelData(0); // Use first channel
    
    // Create a slice only if the range is valid
    const segment = channelData.slice(startSample, endSample);

    if (segment.length === 0) return 0;

    let sumOfSquares = 0;
    for (let i = 0; i < segment.length; i++) {
      sumOfSquares += segment[i] * segment[i];
    }
    const rms = Math.sqrt(sumOfSquares / segment.length);
    return Math.min(rms * PULSE_LEVELS[pulseIntensity].rmsBoost, 1.0); // Use intensity-specific boost
  };


  // Animation loop for pulsing effect
  useEffect(() => {
    const audioEl = audioElementRef.current;

    const animate = () => {
      if (!isPlaying || selectedPreset !== "pulse" || !audioEl || !audioBufferRef.current) {
        setCurrentScale(prevScale => prevScale * 0.85 + 1.0 * 0.15); // Smoothly return to base
        animationFrameIdRef.current = requestAnimationFrame(animate);
        return;
      }
      
      const currentTime = audioEl.currentTime;
      const audioEnergy = calculateAudioEnergyAtTime(currentTime);
      
      const targetScale = 1.0 + (audioEnergy * PULSE_LEVELS[pulseIntensity].maxScale);
      
      setCurrentScale(prevScale => prevScale * 0.6 + targetScale * 0.4); // Smooth transition
      animationFrameIdRef.current = requestAnimationFrame(animate);
    };

    if (selectedPreset === "pulse") {
      animationFrameIdRef.current = requestAnimationFrame(animate);
    } else {
      setCurrentScale(1.0); 
      if (animationFrameIdRef.current) {
        cancelAnimationFrame(animationFrameIdRef.current);
        animationFrameIdRef.current = null;
      }
    }

    return () => {
      if (animationFrameIdRef.current) {
        cancelAnimationFrame(animationFrameIdRef.current);
        animationFrameIdRef.current = null;
      }
      setCurrentScale(1.0); 
    };
  }, [isPlaying, selectedPreset, audioElementRef, songDataUriForPulse, pulseIntensity]); // songDataUriForPulse ensures re-evaluation if audio source for analysis changes

  const dynamicStyle = {
    transform: `scale(${currentScale})`,
    transition: 'transform 0.05s ease-out', // Fast transition for reactivity
    // Ensure the image/video scales from its center
    transformOrigin: 'center center', 
  };

  // Base style for contain or cover, depending on media type or preference
  const baseObjectFitStyle = visualMediaType === 'video' ? { objectFit: "contain" } : { objectFit: "cover" } as React.CSSProperties;
  // For pulse, we want to ensure it scales from center, object-fit: contain might be better to see the whole scaled item.
  const pulseObjectFitStyle = { objectFit: "contain" } as React.CSSProperties; 

  const imageStyle = selectedPreset === "pulse" 
    ? { ...pulseObjectFitStyle, ...dynamicStyle } 
    : baseObjectFitStyle;
  
  const videoStyle = selectedPreset === "pulse" 
    ? { ...pulseObjectFitStyle, ...dynamicStyle, width: '100%', height: '100%' } // ensure video fills container for transform
    : { ...baseObjectFitStyle, width: '100%', height: '100%' };


  return (
    <div className="relative w-full aspect-video bg-muted rounded-lg overflow-hidden shadow-2xl">
      {!visualMediaSrc && (
        <Image
          src="https://picsum.photos/1280/720" // Placeholder
          alt="Preview placeholder"
          fill
          sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
          style={imageStyle}
          className={cn(imageStyle.objectFit === "cover" ? "object-cover" : "object-contain")}
          data-ai-hint="abstract music"
          priority
        />
      )}
      {visualMediaSrc && visualMediaType === "image" && (
        <Image
          src={visualMediaSrc}
          alt="Uploaded visual"
          fill
          sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
          style={imageStyle}
          className={cn(imageStyle.objectFit === "cover" ? "object-cover" : "object-contain")}
          priority
        />
      )}
      {visualMediaSrc && visualMediaType === "video" && (
        <video
          src={visualMediaSrc}
          // className is tricky with dynamic style vs class object-fit. Style prop takes precedence.
          className={cn("w-full h-full", videoStyle.objectFit === "cover" ? "object-cover" : "object-contain")}
          style={videoStyle}
          playsInline
          loop
          muted // Important: user controls main audio via RhythmicCanvasApp's audio element
          autoPlay
        />
      )}
    </div>
  );
}

