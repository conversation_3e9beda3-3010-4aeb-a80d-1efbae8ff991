'use client';

import { getAudioEnergyAtTime } from './audio-utils';

// Constants for pulse effect
const ANALYSIS_WINDOW_DURATION_SEC = 0.05; // 50ms window for audio analysis
const PULSE_MAX_SCALE_INCREASE = 0.3;    // e.g., visual scale from 1.0 to 1.3

// Constants for extreme memory optimization
const BATCH_SIZE = 10; // Process only 10 frames at a time
const JPEG_QUALITY = 0.75; // Further reduce quality to save memory
const MAX_FRAMES_PER_REQUEST = 300; // Maximum frames to send in a single request

interface ExportParams {
  songDataUri: string;
  visualMediaSrc: string;
  visualMediaType: "image" | "video";
  outputWidth: number;
  outputHeight: number;
  fps: number;
  onProgress: (progress: number, info?: string) => void;
}

interface ExportResult {
  blob: Blob;
  actualMimeType: string;
}

// Helper function to load an image
async function loadImage(src: string): Promise<HTMLImageElement> {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.crossOrigin = "anonymous";
    img.onload = () => resolve(img);
    img.onerror = (e) => reject(new Error(`Failed to load image: ${e}`));
    img.src = src;
  });
}

// Helper function to load a video
async function loadVideo(src: string): Promise<HTMLVideoElement> {
  return new Promise((resolve, reject) => {
    const video = document.createElement('video');
    video.crossOrigin = "anonymous";
    video.muted = true;
    video.onloadedmetadata = () => resolve(video);
    video.onerror = (e) => reject(new Error(`Failed to load video: ${e}`));
    video.src = src;
    video.load();
  });
}

async function convertAudioToMP3(audioContext: AudioContext, audioBuffer: AudioBuffer): Promise<Blob> {
  return new Promise((resolve, reject) => {
    try {
      // Create an offline context for rendering
      const offlineCtx = new OfflineAudioContext(
        audioBuffer.numberOfChannels,
        audioBuffer.length,
        audioBuffer.sampleRate
      );
      
      // Create a buffer source
      const source = offlineCtx.createBufferSource();
      source.buffer = audioBuffer;
      source.connect(offlineCtx.destination);
      
      // Start rendering
      source.start(0);
      offlineCtx.startRendering().then(renderedBuffer => {
        // Convert the rendered buffer to WAV format
        const numberOfChannels = renderedBuffer.numberOfChannels;
        const length = renderedBuffer.length;
        const sampleRate = renderedBuffer.sampleRate;
        const bitsPerSample = 16;
        const bytesPerSample = bitsPerSample / 8;
        const blockAlign = numberOfChannels * bytesPerSample;
        const byteRate = sampleRate * blockAlign;
        const dataSize = length * blockAlign;
        
        const buffer = new ArrayBuffer(44 + dataSize);
        const view = new DataView(buffer);
        
        // Write WAV header
        writeString(view, 0, 'RIFF');
        view.setUint32(4, 36 + dataSize, true);
        writeString(view, 8, 'WAVE');
        writeString(view, 12, 'fmt ');
        view.setUint32(16, 16, true);
        view.setUint16(20, 1, true);
        view.setUint16(22, numberOfChannels, true);
        view.setUint32(24, sampleRate, true);
        view.setUint32(28, byteRate, true);
        view.setUint16(32, blockAlign, true);
        view.setUint16(34, bitsPerSample, true);
        writeString(view, 36, 'data');
        view.setUint32(40, dataSize, true);
        
        // Write audio data
        const offset = 44;
        const channelData = [];
        for (let i = 0; i < numberOfChannels; i++) {
          channelData.push(renderedBuffer.getChannelData(i));
        }
        
        for (let i = 0; i < length; i++) {
          for (let channel = 0; channel < numberOfChannels; channel++) {
            const sample = Math.max(-1, Math.min(1, channelData[channel][i]));
            const value = sample < 0 ? sample * 0x8000 : sample * 0x7FFF;
            view.setInt16(offset + (i * blockAlign) + (channel * bytesPerSample), value, true);
          }
        }
        
        // Create a blob from the buffer
        const wavBlob = new Blob([buffer], { type: 'audio/wav' });
        resolve(wavBlob);
      }).catch(err => {
        reject(err);
      });
    } catch (err) {
      reject(err);
    }
  });
  
  function writeString(view: DataView, offset: number, string: string) {
    for (let i = 0; i < string.length; i++) {
      view.setUint8(offset + i, string.charCodeAt(i));
    }
  }
}

/**
 * Ultra memory-optimized server-side export function
 */
export async function exportWithImprovedServerFFmpeg({
  songDataUri,
  visualMediaSrc,
  visualMediaType,
  outputWidth,
  outputHeight,
  fps,
  onProgress,
}: ExportParams): Promise<ExportResult> {
  console.log(`[ImprovedServerExporter] Starting export. Media type: ${visualMediaType}`);
  onProgress(0.0, "Initializing export...");

  try {
    // 1. Load and decode audio
    onProgress(0.05, "Decoding audio...");
    const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)({
      sampleRate: 48000 // High-quality audio
    });
    
    const audioResponse = await fetch(songDataUri);
    const audioArrayBuffer = await audioResponse.arrayBuffer();
    const audioBuffer = await audioContext.decodeAudioData(audioArrayBuffer);
    
    const audioDuration = audioBuffer.duration;
    console.log(`[ImprovedServerExporter] Audio decoded. Duration: ${audioDuration.toFixed(3)}s, Sample rate: ${audioBuffer.sampleRate}Hz`);
    onProgress(0.1, `Audio decoded (${audioDuration.toFixed(1)}s)`);
    
    // 2. Create a job on the server first
    const jobResponse = await fetch('/api/export-video/create-job', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        duration: audioDuration,
        fps: fps,
        width: outputWidth,
        height: outputHeight,
      }),
    });
    
    if (!jobResponse.ok) {
      throw new Error(`Failed to create export job: ${await jobResponse.text()}`);
    }
    
    const { jobId } = await jobResponse.json();
    console.log(`[ImprovedServerExporter] Created job: ${jobId}`);
    
    // 3. Upload audio file separately - IMPROVED VERSION
    try {
      // First try to get the audio directly from the data URI
      const audioBlob = await fetch(songDataUri).then(r => r.blob());
      
      // If the blob is too small or empty, it might be corrupted
      if (audioBlob.size < 1000) {
        throw new Error("Audio blob is too small, likely corrupted");
      }
      
      // Ensure we have a proper audio/mpeg MIME type
      let audioToUpload;
      if (audioBlob.type === 'audio/mpeg' || audioBlob.type === 'audio/mp3') {
        audioToUpload = audioBlob;
      } else {
        // If the MIME type is not correct, create a new blob with the correct type
        audioToUpload = new Blob([await audioBlob.arrayBuffer()], { type: 'audio/mpeg' });
      }
      
      console.log(`[ImprovedServerExporter] Audio blob prepared: size=${audioToUpload.size}, type=${audioToUpload.type}`);
      
      const audioFormData = new FormData();
      audioFormData.append('jobId', jobId);
      audioFormData.append('audio', audioToUpload, 'audio.mp3');
      
      const audioUploadResponse = await fetch('/api/export-video/upload-audio', {
        method: 'POST',
        body: audioFormData,
      });
      
      if (!audioUploadResponse.ok) {
        throw new Error(`Failed to upload audio: ${await audioUploadResponse.text()}`);
      }
    } catch (error) {
      console.warn(`[ImprovedServerExporter] Failed to upload audio directly: ${error}. Trying fallback method...`);
      
      // Fallback: Convert the audio buffer to WAV and then upload
      try {
        const wavBlob = await convertAudioToMP3(audioContext, audioBuffer);
        
        const audioFormData = new FormData();
        audioFormData.append('jobId', jobId);
        audioFormData.append('audio', wavBlob, 'audio.wav');
        
        const audioUploadResponse = await fetch('/api/export-video/upload-audio', {
          method: 'POST',
          body: audioFormData,
        });
        
        if (!audioUploadResponse.ok) {
          throw new Error(`Failed to upload audio (fallback): ${await audioUploadResponse.text()}`);
        }
        
        console.log(`[ImprovedServerExporter] Audio uploaded using fallback method`);
      } catch (fallbackError) {
        throw new Error(`Failed to upload audio (both methods): ${fallbackError}`);
      }
    }

    console.log(`[ImprovedServerExporter] Audio uploaded for job: ${jobId}`);
    onProgress(0.15, `Audio uploaded`);
    
    // 4. Load visual media (image or video)
    onProgress(0.2, `Loading ${visualMediaType}...`);
    const visualMedia = visualMediaType === "image" 
      ? await loadImage(visualMediaSrc)
      : await loadVideo(visualMediaSrc);
    
    console.log(`[ImprovedServerExporter] Visual media loaded`);
    
    // 5. Set up canvas for rendering frames
    const canvas = document.createElement('canvas');
    canvas.width = outputWidth;
    canvas.height = outputHeight;
    const ctx = canvas.getContext('2d', { alpha: false });
    
    if (!ctx) {
      throw new Error("Failed to get 2D context from canvas");
    }
    
    // 6. Calculate base dimensions for visual media
    const mediaAspectRatio = visualMediaType === "image"
      ? (visualMedia as HTMLImageElement).naturalWidth / (visualMedia as HTMLImageElement).naturalHeight
      : (visualMedia as HTMLVideoElement).videoWidth / (visualMedia as HTMLVideoElement).videoHeight;
    
    const canvasAspectRatio = outputWidth / outputHeight;
    
    let baseDrawWidth: number, baseDrawHeight: number;
    if (mediaAspectRatio > canvasAspectRatio) {
      baseDrawWidth = outputWidth;
      baseDrawHeight = outputWidth / mediaAspectRatio;
    } else {
      baseDrawHeight = outputHeight;
      baseDrawWidth = outputHeight * mediaAspectRatio;
    }
    
    // 7. Calculate total frames and prepare for batch processing
    const numFrames = Math.ceil(audioDuration * fps);
    const numBatches = Math.ceil(numFrames / BATCH_SIZE);
    
    // If it's a video, ensure it's ready for seeking
    if (visualMediaType === "video") {
      const videoElement = visualMedia as HTMLVideoElement;
      videoElement.currentTime = 0;
      await new Promise<void>(resolve => {
        videoElement.onseeked = () => resolve();
      });
    }
    
    // 8. Process frames in micro-batches to minimize memory usage
    onProgress(0.25, "Processing frames in micro-batches...");
    
    // Calculate how many API requests we'll need
    const framesPerRequest = Math.min(MAX_FRAMES_PER_REQUEST, numFrames);
    const numRequests = Math.ceil(numFrames / framesPerRequest);
    
    for (let requestIndex = 0; requestIndex < numRequests; requestIndex++) {
      const requestStartFrame = requestIndex * framesPerRequest;
      const requestEndFrame = Math.min((requestIndex + 1) * framesPerRequest, numFrames);
      const requestFrameCount = requestEndFrame - requestStartFrame;
      
      // Create a new FormData for each request
      const formData = new FormData();
      formData.append('jobId', jobId);
      formData.append('startFrame', requestStartFrame.toString());
      formData.append('frameCount', requestFrameCount.toString());
      
      // Process micro-batches within this request
      const requestBatches = Math.ceil(requestFrameCount / BATCH_SIZE);
      
      for (let batchIndex = 0; batchIndex < requestBatches; batchIndex++) {
        const batchStartFrame = requestStartFrame + (batchIndex * BATCH_SIZE);
        const batchEndFrame = Math.min(requestStartFrame + ((batchIndex + 1) * BATCH_SIZE), requestEndFrame);
        
        const overallProgress = 0.25 + (
          (requestIndex * framesPerRequest + batchIndex * BATCH_SIZE) / numFrames
        ) * 0.65;
        
        onProgress(
          overallProgress,
          `Processing frames ${batchStartFrame + 1}-${batchEndFrame} of ${numFrames}`
        );
        
        // Process each frame in the current micro-batch
        for (let i = batchStartFrame; i < batchEndFrame; i++) {
          const frameTime = i / fps;
          
          // Get audio energy for this frame
          const audioEnergy = getAudioEnergyAtTime(audioBuffer, frameTime, ANALYSIS_WINDOW_DURATION_SEC);
          const scale = 1.0 + audioEnergy * PULSE_MAX_SCALE_INCREASE;
          
          // If it's a video, seek to the correct time
          if (visualMediaType === "video") {
            const videoElement = visualMedia as HTMLVideoElement;
            videoElement.currentTime = frameTime % videoElement.duration;
            await new Promise<void>(resolve => {
              videoElement.onseeked = () => resolve();
            });
          }
          
          // Clear canvas with black background
          ctx.fillStyle = 'black';
          ctx.fillRect(0, 0, outputWidth, outputHeight);
          
          // Calculate dimensions with pulse effect
          const finalDrawWidth = baseDrawWidth * scale;
          const finalDrawHeight = baseDrawHeight * scale;
          const offsetX = (outputWidth - finalDrawWidth) / 2;
          const offsetY = (outputHeight - finalDrawHeight) / 2;
          
          // Draw visual media with pulse effect
          ctx.drawImage(visualMedia, offsetX, offsetY, finalDrawWidth, finalDrawHeight);
          
          // Convert canvas to JPEG with reduced quality to save memory
          const frameDataUrl = canvas.toDataURL('image/jpeg', JPEG_QUALITY);
          
          // Add frame to FormData
          formData.append(`frame_${i - requestStartFrame}`, frameDataUrl);
          
          // Log progress periodically
          if ((i - batchStartFrame) % Math.max(1, Math.floor(BATCH_SIZE / 2)) === 0 || i === batchEndFrame - 1) {
            console.log(`[ImprovedServerExporter] Frame ${i+1}/${numFrames} generated`);
          }
        }
        
        // Force garbage collection between batches
        await new Promise(resolve => setTimeout(resolve, 100));
      }
      
      // Send this batch of frames to the server
      console.log(`[ImprovedServerExporter] Uploading frames ${requestStartFrame+1}-${requestEndFrame} to server...`);
      
      const uploadResponse = await fetch('/api/export-video/upload-frames', {
        method: 'POST',
        body: formData,
      });
      
      if (!uploadResponse.ok) {
        throw new Error(`Failed to upload frames: ${await uploadResponse.text()}`);
      }
      
      console.log(`[ImprovedServerExporter] Frames ${requestStartFrame+1}-${requestEndFrame} uploaded successfully`);
      
      // Clear FormData to free memory
      for (const key of Array.from(formData.keys())) {
        formData.delete(key);
      }
      
      // Force garbage collection between requests
      await new Promise(resolve => setTimeout(resolve, 200));
    }
    
    // 9. Tell the server to process the video
    onProgress(0.9, "All frames uploaded. Processing video...");
    
    const processResponse = await fetch('/api/export-video/process', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ jobId }),
    });
    
    if (!processResponse.ok) {
      throw new Error(`Failed to process video: ${await processResponse.text()}`);
    }
    
    // 10. Download the processed video
    onProgress(0.95, "Video processed. Downloading...");
    
    const downloadResponse = await fetch(`/api/export-video/download?jobId=${jobId}`);
    
    if (!downloadResponse.ok) {
      throw new Error(`Failed to download video: ${await downloadResponse.text()}`);
    }
    
    const videoBlob = await downloadResponse.blob();
    
    onProgress(1.0, "Export complete!");
    return {
      blob: videoBlob,
      actualMimeType: 'video/mp4'
    };
  } catch (error) {
    console.error('[ImprovedServerExporter] Export failed:', error);
    throw error;
  }
}
