import { NextRequest } from 'next/server';
import { existsSync } from 'fs';
import path from 'path';
import os from 'os';
import { unlink, readFile } from 'fs/promises';

export async function GET(request: NextRequest) {
  try {
    // Get jobId from query parameters
    const { searchParams } = new URL(request.url);
    const jobId = searchParams.get('jobId');
    
    // Validate required parameters
    if (!jobId) {
      return new Response(
        JSON.stringify({ error: 'Missing jobId' }),
        { status: 400, headers: { 'Content-Type': 'application/json' } }
      );
    }
    
    // Check if output file exists
    const jobDir = path.join(os.tmpdir(), 'rhythmic-canvas-exports', jobId);
    const outputPath = path.join(jobDir, 'output.mp4');
    
    if (!existsSync(outputPath)) {
      return new Response(
        JSON.stringify({ error: 'Video not found' }),
        { status: 404, headers: { 'Content-Type': 'application/json' } }
      );
    }
    
    // Read the output file
    const videoBuffer = await readFile(outputPath);
    
    // Schedule cleanup after response is sent
    setTimeout(async () => {
      try {
        await unlink(outputPath);
        console.log(`Deleted output file for job ${jobId}`);
      } catch (error) {
        console.error(`Error deleting output file for job ${jobId}:`, error);
      }
    }, 5000);
    
    // Return the video file
    return new Response(videoBuffer, {
      headers: {
        'Content-Type': 'video/mp4',
        'Content-Disposition': 'attachment; filename="rhythmic-canvas-video.mp4"'
      }
    });
  } catch (error) {
    console.error('Error downloading video:', error);
    return new Response(
      JSON.stringify({ error: `Failed to download video: ${error}` }),
      { status: 500, headers: { 'Content-Type': 'application/json' } }
    );
  }
}