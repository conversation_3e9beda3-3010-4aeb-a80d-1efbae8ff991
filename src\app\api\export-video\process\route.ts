import { NextRequest } from 'next/server';
import { existsSync } from 'fs';
import path from 'path';
import { exec } from 'child_process';
import { promisify } from 'util';
import os from 'os';
import { readdir } from 'fs/promises';

const execAsync = promisify(exec);

export async function POST(request: NextRequest) {
  try {
    // Parse request body
    const { jobId } = await request.json();
    
    // Validate required parameters
    if (!jobId) {
      return new Response(
        JSON.stringify({ error: 'Missing jobId' }),
        { status: 400, headers: { 'Content-Type': 'application/json' } }
      );
    }
    
    // Check if job directory exists
    const jobDir = path.join(os.tmpdir(), 'rhythmic-canvas-exports', jobId);
    const framesDir = path.join(jobDir, 'frames');
    
    if (!existsSync(jobDir) || !existsSync(framesDir)) {
      return new Response(
        JSON.stringify({ error: 'Job not found or incomplete' }),
        { status: 404, headers: { 'Content-Type': 'application/json' } }
      );
    }
    
    // Find the audio file (could be mp3 or wav)
    let audioPath = path.join(jobDir, 'audio.mp3');
    if (!existsSync(audioPath)) {
      audioPath = path.join(jobDir, 'audio.wav');
      if (!existsSync(audioPath)) {
        // List files in the job directory for debugging
        const files = await readdir(jobDir);
        console.error(`No audio file found in job directory. Files present: ${files.join(', ')}`);
        
        return new Response(
          JSON.stringify({ error: 'Audio file not found' }),
          { status: 404, headers: { 'Content-Type': 'application/json' } }
        );
      }
    }
    
    // Check if we have any frames
    const frameFiles = await readdir(framesDir);
    if (frameFiles.length === 0) {
      return new Response(
        JSON.stringify({ error: 'No frames found for this job' }),
        { status: 404, headers: { 'Content-Type': 'application/json' } }
      );
    }
    
    console.log(`Found ${frameFiles.length} frames and audio file: ${audioPath}`);
    
    // Generate FFmpeg command with memory-optimized settings
    const outputPath = path.join(jobDir, 'output.mp4');
    
    // Use a lower CRF value (higher quality) but faster preset to save memory
    // Add threads parameter to limit CPU usage and memory consumption
    const ffmpegCommand = `ffmpeg -y -framerate 30 -i "${framesDir}/frame_%06d.jpg" -i "${audioPath}" -c:v libx264 -preset faster -crf 23 -threads 2 -pix_fmt yuv420p -c:a aac -b:a 128k -shortest "${outputPath}"`;
    
    console.log(`Running FFmpeg command for job ${jobId}: ${ffmpegCommand}`);
    
    // Execute FFmpeg command
    const { stdout, stderr } = await execAsync(ffmpegCommand);
    
    if (!existsSync(outputPath)) {
      console.error(`FFmpeg failed to create output file. stderr: ${stderr}`);
      return new Response(
        JSON.stringify({ error: 'Failed to create video file' }),
        { status: 500, headers: { 'Content-Type': 'application/json' } }
      );
    }
    
    console.log(`Video created for job ${jobId}: ${outputPath}`);
    
    // Return success
    return new Response(
      JSON.stringify({ 
        success: true,
        outputPath
      }),
      { status: 200, headers: { 'Content-Type': 'application/json' } }
    );
  } catch (error) {
    console.error('Error processing video:', error);
    return new Response(
      JSON.stringify({ error: `Failed to process video: ${error}` }),
      { status: 500, headers: { 'Content-Type': 'application/json' } }
    );
  }
}

